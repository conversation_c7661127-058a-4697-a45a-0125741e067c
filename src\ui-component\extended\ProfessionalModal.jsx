import React from 'react';
import PropTypes from 'prop-types';
import { Dialog, DialogTitle, DialogContent, DialogActions, IconButton, Typography, Box, CircularProgress, Fade } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { COLORS, TYPOGRAPHY, SPACING, SHADOWS, BORDER_RADIUS } from '../../themes/designSystem';
import './ProfessionalModal.css';

// Professional Modal Header Component
export const ModalHeader = ({ title, subtitle, icon, variant = 'primary', onClose, showCloseButton = true }) => {
  const getVariantColors = () => {
    switch (variant) {
      case 'danger':
        return {
          background: `linear-gradient(135deg, ${COLORS.error.main} 0%, ${COLORS.error.dark} 100%)`,
          color: '#ffffff'
        };
      case 'warning':
        return {
          background: `linear-gradient(135deg, ${COLORS.warning.main} 0%, ${COLORS.warning.dark} 100%)`,
          color: COLORS.text.dark
        };
      case 'success':
        return {
          background: `linear-gradient(135deg, ${COLORS.success.main} 0%, ${COLORS.success.dark} 100%)`,
          color: '#ffffff'
        };
      case 'info':
        return {
          background: `linear-gradient(135deg, ${COLORS.info.main} 0%, ${COLORS.info.dark} 100%)`,
          color: '#ffffff'
        };
      case 'secondary':
        return {
          background: `linear-gradient(135deg, ${COLORS.grey[600]} 0%, ${COLORS.grey[700]} 100%)`,
          color: '#ffffff'
        };
      default:
        return {
          background: `linear-gradient(135deg, ${COLORS.primary.main} 0%, ${COLORS.primary.dark} 100%)`,
          color: '#ffffff'
        };
    }
  };

  const variantColors = getVariantColors();

  return (
    <Box
      sx={{
        background: `linear-gradient(135deg, ${variantColors.background})`,
        padding: 0,
        borderRadius: `${BORDER_RADIUS.lg} ${BORDER_RADIUS.lg} 0 0`,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `
            radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255,255,255,0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(255,255,255,0.06) 0%, transparent 50%)
          `,
          zIndex: 1
        }}
      />

      {/* Main Header Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          padding: `${SPACING.xl} ${SPACING['2xl']}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: '80px'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
          {icon && (
            <Box
              sx={{
                mr: SPACING.xl,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%)',
                borderRadius: '16px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.4)',
                backdropFilter: 'blur(10px)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '2px',
                  left: '2px',
                  right: '2px',
                  height: '50%',
                  background: 'linear-gradient(180deg, rgba(255,255,255,0.2) 0%, transparent 100%)',
                  borderRadius: '12px 12px 0 0'
                }
              }}
            >
              {React.cloneElement(icon, {
                style: {
                  fontSize: '28px',
                  color: 'white',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))',
                  position: 'relative',
                  zIndex: 1
                }
              })}
            </Box>
          )}
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h4"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontSize: TYPOGRAPHY.fontSize['2xl'],
                fontWeight: TYPOGRAPHY.fontWeight.bold,
                margin: 0,
                lineHeight: 1.2,
                color: 'white',
                textShadow: '0 2px 8px rgba(0,0,0,0.3)',
                letterSpacing: '0.5px',
                mb: subtitle ? SPACING.xs : 0
              }}
            >
              {title}
            </Typography>
            {subtitle && (
              <Typography
                variant="body1"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  fontSize: TYPOGRAPHY.fontSize.base,
                  fontWeight: TYPOGRAPHY.fontWeight.medium,
                  opacity: 0.95,
                  lineHeight: 1.4,
                  color: 'white',
                  textShadow: '0 1px 4px rgba(0,0,0,0.2)'
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
        {showCloseButton && (
          <IconButton
            onClick={onClose}
            sx={{
              color: 'white',
              padding: SPACING.sm,
              borderRadius: '12px',
              background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              backdropFilter: 'blur(10px)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.15) 100%)',
                transform: 'scale(1.05)',
                boxShadow: '0 4px 20px rgba(0,0,0,0.2)'
              }
            }}
          >
            <CloseIcon sx={{ fontSize: '20px', filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))' }} />
          </IconButton>
        )}
      </Box>

      {/* Bottom Accent Line */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: 'linear-gradient(90deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0.4) 100%)',
          zIndex: 3
        }}
      />
    </Box>
  );
};

// Professional Modal Footer Component
export const ModalFooter = ({
  children,
  primaryButton,
  secondaryButton,
  loading = false,
  primaryAction,
  secondaryAction,
  primaryVariant = 'primary',
  secondaryVariant = 'secondary',
  primaryText = 'Confirmer',
  secondaryText = 'Annuler',
  loadingText = 'Traitement...',
  disabled = false
}) => {
  const getButtonStyles = (variant) => {
    const baseStyles = {
      fontFamily: TYPOGRAPHY.fontFamily.primary,
      fontWeight: TYPOGRAPHY.fontWeight.medium,
      textTransform: 'none',
      borderRadius: BORDER_RADIUS.md,
      padding: `${SPACING.sm} ${SPACING.lg}`,
      minWidth: '100px',
      fontSize: TYPOGRAPHY.fontSize.sm,
      transition: 'all 0.2s ease',
      '&:hover': {
        transform: 'translateY(-1px)',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      },
      '&:active': {
        transform: 'translateY(0)'
      },
      '&:disabled': {
        opacity: 0.6,
        cursor: 'not-allowed',
        transform: 'none',
        '&:hover': {
          transform: 'none',
          boxShadow: 'none'
        }
      }
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, ${COLORS.primary.main} 0%, ${COLORS.primary.dark} 100%)`,
          color: '#ffffff',
          border: 'none',
          '&:hover': {
            ...baseStyles['&:hover'],
            background: `linear-gradient(135deg, ${COLORS.primary.dark} 0%, ${COLORS.primary.main} 100%)`
          }
        };
      case 'danger':
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, ${COLORS.error.main} 0%, ${COLORS.error.dark} 100%)`,
          color: '#ffffff',
          border: 'none'
        };
      case 'success':
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, ${COLORS.success.main} 0%, ${COLORS.success.dark} 100%)`,
          color: '#ffffff',
          border: 'none'
        };
      case 'secondary':
      default:
        return {
          ...baseStyles,
          backgroundColor: COLORS.grey[600],
          color: '#ffffff',
          border: `1px solid ${COLORS.grey[600]}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.grey[700]
          }
        };
    }
  };

  return (
    <DialogActions
      sx={{
        padding: `${SPACING.lg} ${SPACING.xl}`,
        borderTop: `1px solid ${COLORS.grey[200]}`,
        backgroundColor: COLORS.grey[50],
        borderRadius: `0 0 ${BORDER_RADIUS.lg} ${BORDER_RADIUS.lg}`,
        display: 'flex',
        justifyContent: 'flex-end',
        gap: SPACING.md,
        minHeight: 'auto'
      }}
    >
      {children ? (
        children
      ) : (
        <>
          {secondaryButton !== false && (
            <Box component="button" onClick={secondaryAction} disabled={loading || disabled} sx={getButtonStyles(secondaryVariant)}>
              {secondaryText}
            </Box>
          )}
          {primaryButton !== false && (
            <Box component="button" onClick={primaryAction} disabled={loading || disabled} sx={getButtonStyles(primaryVariant)}>
              {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: SPACING.xs }}>
                  <CircularProgress size={16} color="inherit" />
                  {loadingText}
                </Box>
              ) : (
                primaryText
              )}
            </Box>
          )}
        </>
      )}
    </DialogActions>
  );
};

// Main Professional Modal Component
const ProfessionalModal = ({
  show,
  onHide,
  title,
  subtitle,
  icon,
  children,
  size = 'md',
  centered = true,
  backdrop = 'static',
  keyboard = false,
  variant = 'primary',

  // Header props
  showHeader = true,
  showCloseButton = true,

  // Footer props
  showFooter = true,
  primaryButton = true,
  secondaryButton = true,
  primaryAction,
  secondaryAction,
  primaryVariant = 'primary',
  secondaryVariant = 'secondary',
  primaryText = 'Confirmer',
  secondaryText = 'Annuler',
  loading = false,
  loadingText = 'Traitement...',
  disabled = false,

  // Body props
  bodyClassName = '',
  bodyStyle = {},
  maxHeight = '70vh',

  // Modal props
  className = '',
  dialogClassName = '',

  // Custom components
  customHeader,
  customFooter,

  ...otherProps
}) => {
  const getMaxWidth = () => {
    switch (size) {
      case 'sm':
        return '420px';
      case 'lg':
        return '800px';
      case 'xl':
        return '1000px';
      case 'md':
      default:
        return '600px';
    }
  };

  const handleSecondaryAction = () => {
    if (secondaryAction) {
      secondaryAction();
    } else {
      onHide();
    }
  };

  const dialogStyles = {
    '& .MuiDialog-paper': {
      borderRadius: BORDER_RADIUS.lg,
      boxShadow: SHADOWS.xl,
      overflow: 'hidden',
      background: COLORS.background.paper,
      border: 'none',
      maxWidth: getMaxWidth(),
      width: '100%',
      margin: SPACING.lg,
      marginTop: '88px', // Account for navbar height
      animation: 'modalSlideIn 0.3s ease-out'
    },
    '& .MuiBackdrop-root': {
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      backdropFilter: 'blur(2px)'
    }
  };

  const bodyStyles = {
    padding: SPACING.lg,
    backgroundColor: COLORS.background.paper,
    minHeight: '200px',
    maxHeight,
    overflowY: 'auto',
    '&::-webkit-scrollbar': {
      width: '6px'
    },
    '&::-webkit-scrollbar-track': {
      background: COLORS.grey[100],
      borderRadius: '3px'
    },
    '&::-webkit-scrollbar-thumb': {
      background: COLORS.grey[400],
      borderRadius: '3px',
      '&:hover': {
        background: COLORS.grey[500]
      }
    },
    ...bodyStyle
  };

  return (
    <Dialog
      open={show}
      onClose={onHide}
      maxWidth={false}
      fullWidth={false}
      disableEscapeKeyDown={!keyboard}
      sx={dialogStyles}
      TransitionComponent={Fade}
      TransitionProps={{
        timeout: 300
      }}
      {...otherProps}
    >
      {showHeader &&
        (customHeader || (
          <ModalHeader title={title} subtitle={subtitle} icon={icon} variant={variant} onClose={onHide} showCloseButton={showCloseButton} />
        ))}

      <DialogContent sx={bodyStyles} className={bodyClassName}>
        {children}
      </DialogContent>

      {showFooter &&
        (customFooter || (
          <ModalFooter
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
            primaryAction={primaryAction}
            secondaryAction={handleSecondaryAction}
            primaryVariant={primaryVariant}
            secondaryVariant={secondaryVariant}
            primaryText={primaryText}
            secondaryText={secondaryText}
            loading={loading}
            loadingText={loadingText}
            disabled={disabled}
          />
        ))}
    </Dialog>
  );
};

// PropTypes
ModalHeader.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  icon: PropTypes.node,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info']),
  onClose: PropTypes.func,
  showCloseButton: PropTypes.bool
};

ModalFooter.propTypes = {
  children: PropTypes.node,
  primaryButton: PropTypes.bool,
  secondaryButton: PropTypes.bool,
  loading: PropTypes.bool,
  primaryAction: PropTypes.func,
  secondaryAction: PropTypes.func,
  primaryVariant: PropTypes.string,
  secondaryVariant: PropTypes.string,
  primaryText: PropTypes.string,
  secondaryText: PropTypes.string,
  loadingText: PropTypes.string,
  disabled: PropTypes.bool
};

ProfessionalModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  icon: PropTypes.node,
  children: PropTypes.node.isRequired,
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  centered: PropTypes.bool,
  backdrop: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
  keyboard: PropTypes.bool,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info']),
  showHeader: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  showFooter: PropTypes.bool,
  primaryButton: PropTypes.bool,
  secondaryButton: PropTypes.bool,
  primaryAction: PropTypes.func,
  secondaryAction: PropTypes.func,
  primaryVariant: PropTypes.string,
  secondaryVariant: PropTypes.string,
  primaryText: PropTypes.string,
  secondaryText: PropTypes.string,
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  disabled: PropTypes.bool,
  bodyClassName: PropTypes.string,
  bodyStyle: PropTypes.object,
  maxHeight: PropTypes.string,
  className: PropTypes.string,
  dialogClassName: PropTypes.string,
  customHeader: PropTypes.node,
  customFooter: PropTypes.node
};

export default ProfessionalModal;
